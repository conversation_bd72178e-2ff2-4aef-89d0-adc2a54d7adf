# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest

from test_dir.api.ec.central_portal.central_im import CentralIm


class TestSalesSellingPoint(weeeTest.TestCase):

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_synonyms')
    def test_sales_selling_point(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-Selling Point商品卖点-查询Selling Point页面数据"""
        selling_point_list = CentralIm().selling_point_list(headers=sales_header)
        assert len(selling_point_list["object"]["data"]) > 0, f'查询Selling Point页面数据异常{selling_point_list}'

